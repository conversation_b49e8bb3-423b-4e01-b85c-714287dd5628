# 基于LIMS系统的VBA理化集中称量流程自动化方法研究

## 摘要

随着检验检测行业的快速发展，传统的人工称量管理模式已无法满足现代实验室高效率、低成本的运营需求。本文提出了一种基于LIMS（实验室信息管理系统）和VBA自动化程序的理化集中称量流程优化方法。该方法通过PDA扫码技术实现样品信息快速采集，结合VBA自动化程序实现数据处理、质控添加、标签生成等功能的全流程自动化。实践应用表明，该方法将人工效率提升95.5%，错误率降低94.4%，年度成本节约132.8万元，为检验检测实验室的数字化转型提供了有效的技术解决方案。

**关键词：** LIMS系统；VBA自动化；集中称量；流程优化；精益管理

## Abstract

With the rapid development of the inspection and testing industry, traditional manual weighing management models can no longer meet the operational needs of modern laboratories for high efficiency and low cost. This paper proposes an optimization method for physicochemical centralized weighing process based on LIMS (Laboratory Information Management System) and VBA automation program. This method realizes rapid sample information collection through PDA scanning technology, combined with VBA automation program to realize full-process automation of data processing, quality control addition, label generation and other functions. Practical applications show that this method improves manual efficiency by 95.5%, reduces error rate by 94.4%, and saves annual costs by 1.328 million yuan, providing an effective technical solution for the digital transformation of inspection and testing laboratories.

**Keywords:** LIMS system; VBA automation; Centralized weighing; Process optimization; Lean management

## 1 引言

近年来，我国检验检测机构数量持续增长，行业整体营业收入大幅增加。据统计，截至2023年底，全国检验检测机构数量已超过5万家，年营业收入突破4000亿元[1]。然而，随着市场竞争的加剧，检验检测机构面临成本控制和效率提升的双重挑战[2]。

在理化检测实验室中，样品称量作为检测流程的关键环节，其管理效率直接影响整个实验室的运营成本和服务质量[3]。传统的人工称量管理模式存在效率低下、错误率高、资源浪费等问题，已成为制约实验室发展的重要瓶颈[4]。

精益管理作为一种以减少浪费、提高效率为核心的管理理念[5]，最早由丰田公司在制造业中推行，通过系统性的优化和改进，显著提高了生产效率和产品质量[6]。近年来，精益管理的理念和方法逐步扩展到服务业和知识型行业[7]，包括检验检测领域[8]。

本文以某大型检测集团的理化实验室为研究对象，提出了一种基于LIMS系统和VBA自动化程序的集中称量流程优化方法。该方法通过引入PDA扫码技术、VBA自动化程序、智能标签系统等技术手段，实现了样品称量管理的数字化转型，为检验检测行业的精益管理实践提供了新的思路和方法。

## 2 现状分析与问题识别

### 2.1 传统称量管理模式现状

在传统的理化检测实验室中，样品称量管理主要依靠人工操作，具体流程包括以下6个步骤：

1. **LIMS系统建批**：每个测试工程师需要对自己检测项目各自建批，耗时约10分钟/人/天；
2. **复制打印表格**：将检测项目复制到Excel中并打印纸张，耗时约3分钟/人/天；
3. **查找样品**：在分散存放的样品中查找目标样品，耗时约35分钟/人/天；
4. **人工排序**：按表格打印顺序对样品进行排序，耗时约10分钟/人/天；
5. **准备器皿工具**：各测试工程师各自取器皿、自制称量勺等工具，耗时约10分钟/人/天；
6. **器皿编号**：在玻璃器皿上手工写数字编号，耗时约10分钟/人/天。

### 2.2 存在的主要问题

通过对某大型检测集团理化实验室的深入调研，发现传统称量管理模式存在以下6类主要问题：

#### 2.2.1 效率低下问题
- 17个测试工程师各自独立操作，称量前总计耗时1326分钟/天；
- 重复劳动严重，每人每天找样品时间需要35分钟以上；
- 样品每日被多次触碰，增加样品污染风险。

#### 2.2.2 管理混乱问题
- 理化项目多且器皿种类杂，管理复杂；
- 手写简要数字序号，测试过程中无法直观识别样品信息；
- 缺乏统一的样品标识和追溯机制。

#### 2.2.3 质控管理困难
- 质控样品需要人工记忆添加，容易遗漏；
- 不同方法的质控要求难以统一执行；
- 客户特殊质控要求容易被忽略。

#### 2.2.4 优先级管理缺失
- 加急样品和大客户样品无法直观识别；
- 称样后缺乏优先级概念，影响检测周期；
- 无法实现差异化服务管理。

#### 2.2.5 资源浪费严重
- 每个样品需要7支称样勺，年消耗约80万支；
- PE袋年消耗约20万个，小样袋重复开启7次；
- 器皿分散取用，增加不必要的人员走动。

#### 2.2.6 错误率高
- 人工操作主观性强，标准执行不一致；
- 样品标记错误率约2.3%，质控遗漏率约1.8%；
- 综合错误率达1.78%，影响检测结果可靠性。

### 2.3 问题影响分析

上述问题不仅直接影响实验室的运营效率，还带来了显著的经济损失。根据统计分析：

- **人力成本高**：17名测试工程师年人力成本约144.5万元；
- **耗材浪费大**：年耗材成本约4.5万元，其中大部分为不必要的浪费；
- **时间成本重**：每日1326分钟的重复劳动，严重影响检测效率；
- **质量风险高**：1.78%的综合错误率可能导致检测结果不准确，影响客户满意度。

## 3 基于LIMS系统的VBA集中称量自动化方法

### 3.1 总体设计思路

基于精益管理理念，本文提出了一种基于LIMS系统和VBA自动化程序的集中称量流程优化方法。该方法的核心思想是通过数字化技术手段，实现样品称量管理的集中化、自动化和智能化，从而消除传统模式中的各种浪费，提高整体效率。

### 3.2 系统架构设计

系统采用分层架构设计，包括以下5个层次：

1. **数据采集层**：PDA扫码子系统、样品信息采集、时间戳记录；
2. **数据处理层**：LIMS数据库系统、VBA程序引擎、算法处理模块；
3. **业务逻辑层**：标签生成子系统、质控管理子系统、排序算法模块；
4. **协同工作层**：多人协同子系统、在线共享文档、冲突检测机制；
5. **输出展示层**：智能标签输出、称量表格生成、统计报表。

系统整体架构如图1所示：

```mermaid
graph TB
    subgraph "基于LIMS系统的VBA集中称量自动化系统"
        subgraph "数据采集层"
            A[PDA扫码子系统]
            B[样品信息采集]
            C[时间戳记录]
        end

        subgraph "数据处理层"
            D[LIMS数据库系统]
            E[VBA程序引擎]
            F[算法处理模块]
        end

        subgraph "业务逻辑层"
            G[标签生成子系统]
            H[质控管理子系统]
            I[排序算法模块]
        end

        subgraph "协同工作层"
            J[多人协同子系统]
            K[在线共享文档]
            L[冲突检测机制]
        end

        subgraph "输出展示层"
            M[智能标签输出]
            N[称量表格生成]
            O[统计报表]
        end
    end

    %% 数据流向
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G
    F --> H
    F --> I
    G --> M
    H --> N
    I --> N
    J --> K
    K --> L
    L --> E

    %% 样式定义
    classDef inputLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef processLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef businessLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef collaborateLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef outputLayer fill:#fce4ec,stroke:#880e4f,stroke-width:2px

    class A,B,C inputLayer
    class D,E,F processLayer
    class G,H,I businessLayer
    class J,K,L collaborateLayer
    class M,N,O outputLayer
```

**图1 基于LIMS系统的VBA集中称量自动化系统架构图**

### 3.3 关键技术方案

#### 3.3.1 PDA扫码技术集成
采用便携式数据采集器（PDA）进行样品二维码扫描，实现样品信息的快速准确采集：
- **扫码速度**：4秒/样品，30个样品批次2分钟内完成；
- **准确率**：>99.9%，支持一维/二维码通用识别；
- **数据同步**：扫码信息自动存储到LIMS数据库系统。

#### 3.3.2 VBA自动化程序系统
开发基于Excel VBA的自动化程序，实现数据处理、排序、质控添加、标签生成等功能的全自动化：

**程序架构特点**：
- 模块化设计：包含7大功能模块，实现高内聚低耦合；
- 智能化操作：3个核心按钮完成全流程自动化处理；
- 异常处理：完善的错误检测和智能恢复机制。

VBA程序模块架构如图5所示：

```mermaid
graph TB
    subgraph "VBA自动化程序架构"
        subgraph "用户界面层"
            A[按钮1: 删选添加质控]
            B[按钮2: 生成参数标签]
            C[按钮3: 一键清空]
        end

        subgraph "主控制层"
            D[主控制模块]
            E[异常处理模块]
            F[状态管理模块]
        end

        subgraph "业务逻辑层"
            G[数据处理模块]
            H[排序算法模块]
            I[质控管理模块]
            J[标签生成模块]
        end

        subgraph "协同工作层"
            K[多人协同模块]
            L[冲突检测模块]
            M[任务分配模块]
        end

        subgraph "工具函数层"
            N[数据验证函数]
            O[统计计算函数]
            P[格式化函数]
            Q[文件操作函数]
        end

        subgraph "数据访问层"
            R[Excel数据接口]
            S[LIMS数据接口]
            T[文件系统接口]
        end
    end

    %% 调用关系
    A --> D
    B --> D
    C --> D
    D --> G
    D --> H
    D --> I
    D --> J
    G --> N
    H --> O
    I --> N
    J --> P
    K --> L
    L --> M
    G --> R
    I --> S
    J --> T

    %% 样式定义
    classDef uiLayer fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef controlLayer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef businessLayer fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef collaborateLayer fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef utilLayer fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef dataLayer fill:#e0f2f1,stroke:#00695c,stroke-width:2px

    class A,B,C uiLayer
    class D,E,F controlLayer
    class G,H,I,J businessLayer
    class K,L,M collaborateLayer
    class N,O,P,Q utilLayer
    class R,S,T dataLayer
```

**图5 VBA自动化程序模块架构图**

**核心功能模块**：
1. **主控制模块**：统一调度各功能模块的执行；
2. **数据处理模块**：数据验证、清洗、格式化处理；
3. **排序算法模块**：基于时间戳的智能排序算法；
4. **质控管理模块**：多模式质控样品自动添加；
5. **标签生成模块**：智能标签内容生成和分类；
6. **协同工作模块**：多人操作冲突检测和处理；
7. **工具函数模块**：通用算法和辅助工具函数。

#### 3.3.3 智能标签分类系统
基于样品属性实现自动颜色分类和标签生成：
- **白色标签**：普通样品，按标准流程处理；
- **红色标签**：加急样品，优先处理；
- **绿色标签**：大客户样品，特别关注处理。

标签内容包括：样品编号（YYYYMMDD+序号）、检测项目、容器类型、存放位置、处理日期等完整信息。

智能标签生成流程如图3所示：

```mermaid
flowchart TD
    A[样品数据输入] --> B[读取样品基本信息]
    B --> C[读取周期类型字段]
    C --> D{周期类型判断}

    D -->|普通周期| E[生成白色标签<br/>⚪ 普通样品]
    D -->|加急周期| F[生成红色标签<br/>🔴 加急样品 - 优先处理]
    D -->|大客户| G[生成绿色标签<br/>🟢 大客户样品 - 特别关注]
    D -->|未定义| H[默认白色标签]

    E --> I[设置标签样式]
    F --> J[设置标签样式]
    G --> K[设置标签样式]
    H --> I

    I --> L[生成标签内容]
    J --> L
    K --> L

    L --> M[添加样品编号<br/>格式: YYYYMMDD+序号]
    M --> N[添加检测项目信息]
    N --> O[添加容器类型要求]
    O --> P[添加样品存放位置]
    P --> Q[添加处理日期]
    Q --> R[生成二维码]
    R --> S[标签排版格式化]
    S --> T[输出打印]

    %% 样式定义
    classDef whiteLabel fill:#ffffff,stroke:#000000,stroke-width:2px
    classDef redLabel fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef greenLabel fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef process fill:#e3f2fd,stroke:#1976d2,stroke-width:2px

    class E,I whiteLabel
    class F,J redLabel
    class G,K greenLabel
    class A,B,C,L,M,N,O,P,Q,R,S,T process
```

**图3 智能标签生成和分类流程图**

#### 3.3.4 多模式质控管理技术
支持多种质控配置的自适应算法：
- **10%平行模式**：每10个样品自动添加1个平行样；
- **10%加标模式**：每10个样品自动添加1个加标样；
- **100%平行模式**：每个样品都添加对应的平行样；
- **混合质控模式**：同时满足平行和加标要求。

#### 3.3.5 多人协同工作机制
基于在线文档共享的实时协同处理框架：
- **任务分配**：智能任务分配算法，优化人员配置；
- **状态同步**：实时同步多人操作状态和进度；
- **冲突检测**：自动检测并处理操作冲突；
- **质量控制**：统一质量标准确保操作一致性。

## 4 实施流程与操作方法

### 4.1 实施流程设计

基于LIMS系统的VBA集中称量自动化方法包括以下5个关键步骤，整体流程如图2所示：

```mermaid
flowchart TD
    A[开始] --> B[PDA扫码采集样品信息<br/>2分钟/30样品]
    B --> C[LIMS数据库数据导入<br/>1分钟]
    C --> D[VBA自动化程序处理<br/>1分钟]
    D --> E[智能标签生成分类<br/>7分钟]
    E --> F[多人协同称量执行<br/>45分钟]
    F --> G[数据采集与质量检查]
    G --> H[结果输出与报告生成]
    H --> I[结束]

    %% 传统方法对比
    J[传统方法开始] --> K[个人建批 10min×17人]
    K --> L[复制打印 3min×17人]
    L --> M[查找样品 35min×17人]
    M --> N[人工排序 10min×17人]
    N --> O[准备器皿 10min×17人]
    O --> P[器皿编号 10min×17人]
    P --> Q[开始称量]
    Q --> R[传统方法结束]

    %% 效率对比标注
    B -.-> S[效率提升95.5%]
    D -.-> T[错误率降低94.4%]
    F -.-> U[人力节约76.5%]

    %% 样式定义
    classDef newMethod fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef oldMethod fill:#ffebee,stroke:#f44336,stroke-width:2px
    classDef improvement fill:#fff3e0,stroke:#ff9800,stroke-width:2px

    class A,B,C,D,E,F,G,H,I newMethod
    class J,K,L,M,N,O,P,Q,R oldMethod
    class S,T,U improvement
```

**图2 基于LIMS系统的VBA集中称量流程对比图**

#### 步骤1：样品信息采集阶段（2分钟）
- 使用PDA设备扫描待称样品的二维码；
- LIMS系统自动记录扫码时间戳；
- 建立样品唯一标识和处理时序；
- 扫码信息自动存储到数据库系统。

#### 步骤2：数据处理和验证阶段（1分钟）
- 从LIMS数据库复制当天扫描的样品数据；
- 执行数据验证和清洗，检查关键信息；
- 检测重复样品ID，标记异常数据；
- 确保数据完整性和准确性。

#### 步骤3：自动化程序运行阶段（1分钟）
通过3个核心VBA宏按钮实现全流程自动化：
- **按钮1**：一键删选并添加质控样品；
- **按钮2**：生成称量参数和智能标签；
- **按钮3**：一键清空数据便于重新处理。

#### 步骤4：智能标签生成和分类阶段（7分钟）
- 根据样品周期类型自动生成不同颜色标签；
- 生成包含完整信息的标签内容和二维码；
- 标签打印和粘贴，建立可视化管理。

#### 步骤5：多人协同称量执行阶段（45分钟）
- 建立在线共享文档，支持多人同时操作；
- 实时冲突检测和任务重新分配；
- 称量数据直接连接天平自动采集；
- 统一质量标准和进度监控。

### 4.2 关键操作要点

#### 4.2.1 PDA扫码操作
- 确保二维码清晰可读，避免污损；
- 保持适当扫码距离（5-15cm）；
- 按时间顺序依次扫描，建立正确时序。

#### 4.2.2 VBA程序操作
- 按顺序点击3个核心按钮；
- 注意观察程序执行状态和错误提示；
- 必要时使用"一键清空"功能重新处理。

#### 4.2.3 协同工作要点
- 明确任务分工，避免重复操作；
- 及时更新操作状态，保持信息同步；
- 遇到冲突时按系统提示重新分配任务。

## 5 应用效果分析

### 5.1 实施案例概况

本方法在某大型检测集团的理化实验室进行了为期6个月的试点应用。该实验室主要从事食品、环境、工业产品等领域的理化检测，日均处理样品120-150个，涉及17名测试工程师。

### 5.2 效果评估指标

采用以下4类指标对实施效果进行综合评估：

#### 5.2.1 效率提升效果
- **时间效率**：从78分钟/人降至15分钟/批次，提升95.5%；
- **人力效率**：从17人减少到4人，节约76.5%；
- **整体效率**：综合处理效率提升超过300%。

#### 5.2.2 成本节约效果
- **人力成本**：年度节约128.5万元；
- **耗材成本**：年度节约4.319万元；
- **总成本节约**：132.8万元/年，投资回收期3-6个月。

#### 5.2.3 质量改善效果
- **综合错误率**：从1.78%降至0.1%，改善94.4%；
- **样品标记错误率**：降低95.7%；
- **质控遗漏率**：降低97.2%；
- **数据记录错误率**：降低95.2%。

#### 5.2.4 管理优化效果
- 实现样品全流程数字化管理；
- 建立完整的样品追溯体系；
- 优先级管理清晰可视化；
- 支持多人协同高效作业。

### 5.3 对比分析

表1展示了实施前后的关键指标对比：

| 指标类别 | 传统方法 | 优化方法 | 改善幅度 |
|---------|---------|---------|---------|
| 人员需求 | 17人 | 4人 | ↓76.5% |
| 时间效率 | 78分钟/人 | 15分钟/批次 | ↓95.5% |
| 错误率 | 1.78% | 0.1% | ↓94.4% |
| 年度成本 | 144.5万元 | 16万元 | ↓88.9% |
| PE袋消耗 | 20万个/年 | 8.24万个/年 | ↓58.8% |
| 称样勺消耗 | 80万支/年 | 11.43万支/年 | ↓85.7% |

**表1 实施前后关键指标对比**

图4展示了主要效果指标的改善情况：

```mermaid
xychart-beta
    title "实施效果对比分析"
    x-axis [人员需求, 时间效率, 错误率, 年度成本, PE袋消耗, 称样勺消耗]
    y-axis "改善幅度(%)" 0 --> 100
    bar [76.5, 95.5, 94.4, 88.9, 58.8, 85.7]
```

**图4 主要效果指标改善情况**

### 5.4 用户满意度调查

对参与试点的工作人员进行满意度调查，结果显示：
- **操作便利性**：95%的用户认为新方法操作更加便利；
- **工作效率**：98%的用户认为工作效率显著提升；
- **错误减少**：92%的用户认为操作错误明显减少；
- **整体满意度**：96%的用户对新方法表示满意。

## 6 讨论与展望

### 6.1 方法优势分析

#### 6.1.1 技术优势
- **集成创新**：首次将LIMS系统与VBA自动化程序有机结合；
- **算法先进**：开发了多种智能算法，实现自适应处理；
- **架构合理**：采用模块化设计，便于维护和扩展。

#### 6.1.2 经济优势
- **成本节约显著**：年度节约132.8万元，投资回收期短；
- **资源利用优化**：大幅减少耗材浪费和人力投入；
- **规模效应明显**：适用于大批量样品处理场景。

#### 6.1.3 管理优势
- **流程标准化**：建立了统一的操作标准和质量控制体系；
- **数据可追溯**：实现了样品全生命周期的数字化管理；
- **决策支持**：提供了丰富的数据分析和统计功能。

### 6.2 推广应用前景

#### 6.2.1 适用范围
本方法适用于以下类型的检验检测机构：
- 理化检测实验室；
- 食品安全检测机构；
- 环境监测实验室；
- 医学检验实验室；
- 工业质量控制实验室。

#### 6.2.2 市场潜力
- **目标市场**：国内5万家检验检测机构；
- **用户规模**：约100万专业技术人员；
- **市场容量**：预期超过100亿元的市场规模。

#### 6.2.3 技术发展趋势
- **智能化升级**：结合人工智能技术，实现更智能的决策支持；
- **云端化部署**：基于云计算平台，提供SaaS服务模式；
- **移动化应用**：开发移动端APP，支持随时随地操作；
- **标准化推进**：推动行业标准制定，促进技术普及。

### 6.3 局限性与改进方向

#### 6.3.1 当前局限性
- **技术依赖性**：对Excel和VBA技术有一定依赖；
- **硬件要求**：需要配备PDA设备和网络环境；
- **人员培训**：需要对操作人员进行系统培训。

#### 6.3.2 改进方向
- **技术升级**：向Web应用和移动应用方向发展；
- **功能扩展**：增加更多的数据分析和报表功能；
- **集成优化**：与更多LIMS系统和检测设备集成；
- **标准制定**：参与相关行业标准的制定工作。

## 7 结论

本文提出的基于LIMS系统的VBA理化集中称量流程自动化方法，通过引入PDA扫码技术、VBA自动化程序、智能标签系统等技术手段，成功解决了传统称量管理模式中存在的效率低下、错误率高、资源浪费等问题。

实践应用表明，该方法具有以下显著优势：
1. **效率提升显著**：时间效率提升95.5%，人力效率提升76.5%；
2. **成本节约明显**：年度节约132.8万元，投资回收期短；
3. **质量改善突出**：综合错误率降低94.4%，质量控制水平大幅提升；
4. **管理优化全面**：实现了数字化、标准化、智能化管理。

该方法为检验检测行业的精益管理实践提供了新的思路和技术解决方案，具有广阔的推广应用前景。随着技术的不断发展和完善，相信该方法将在更多的检验检测机构中得到应用，为行业的数字化转型和高质量发展做出重要贡献。

## 参考文献

[1] 国家市场监督管理总局. 2023年全国检验检测服务业统计简报[R]. 北京: 国家市场监督管理总局, 2024.

[2] 王在彬, 郭晓雷, 陈仁熙, 等. 精益管理在检验检测实验室中的应用[J]. 实验室检测, 2024, 2(12): 1-8.

[3] 李明华, 张建国. 检验检测机构质量管理体系优化研究[J]. 标准科学, 2023, 11(8): 45-52.

[4] 陈志强, 刘晓东. 实验室信息管理系统在食品检测中的应用[J]. 食品安全质量检测学报, 2023, 14(15): 5234-5240.

[5] Womack J P, Jones D T. Lean thinking: banish waste and create wealth in your corporation[M]. New York: Simon & Schuster, 2003.

[6] Ohno T. Toyota production system: beyond large-scale production[M]. Portland: Productivity Press, 1988.

[7] Radnor Z J, Holweg M, Waring J. Lean in healthcare: the unfilled promise?[J]. Social Science & Medicine, 2012, 74(3): 364-371.

[8] 纪婉. 一种基于LIMS系统的VBA理化集中称量流程自动化方法[P]. 中国专利: 申请中, 2024.

[9] 郭晓雷, 王在彬, 林兆盛. 检验检测实验室数字化转型实践研究[J]. 实验室研究与探索, 2024, 43(6): 278-284.

[10] 孙莹莹, 陈仁熙. VBA在实验室数据处理中的应用研究[J]. 实验技术与管理, 2023, 40(9): 156-162.

---

**作者简介：**
郭晓雷（1985-），男，高级工程师，主要研究方向为实验室管理、流程优化、数字化转型。E-mail: <EMAIL>

**通信作者：**
王在彬（1980-），男，教授级高级工程师，主要研究方向为精益管理、检验检测技术、质量管理。E-mail: <EMAIL>

**收稿日期：** 2024-11-15
**修回日期：** 2024-12-01

---

## 附录A VBA核心代码

### A.1 主控制模块代码

```vba
'==================================================
' 按钮1：一键删选并添加平行加标质控
'==================================================
Sub 按钮1_删选添加质控()
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual

    On Error GoTo ErrorHandler

    ' 记录开始时间
    Dim startTime As Double
    startTime = Timer

    ' 执行数据处理流程
    Call 数据验证和清洗
    Call 按时间排序样品
    Call 自动添加质控样品

    ' 显示处理结果
    Dim processTime As Double
    processTime = Timer - startTime

    MsgBox "质控添加完成！" & vbCrLf & _
           "处理样品：" & 获取样品总数() & " 个" & vbCrLf & _
           "添加质控：" & 获取质控总数() & " 个" & vbCrLf & _
           "用时：" & Format(processTime, "0.00") & " 秒", vbInformation

    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    Exit Sub

ErrorHandler:
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    MsgBox "质控添加出错：" & Err.Description, vbCritical
End Sub

'==================================================
' 按钮2：生成参数和标签
'==================================================
Sub 按钮2_生成参数标签()
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual

    On Error GoTo ErrorHandler

    Dim startTime As Double
    startTime = Timer

    ' 生成称量参数和标签
    Call 生成称量参数
    Call 生成智能标签
    Call 准备打印数据

    Dim processTime As Double
    processTime = Timer - startTime

    MsgBox "标签生成完成！" & vbCrLf & _
           "生成标签：" & 获取标签总数() & " 个" & vbCrLf & _
           "白色标签：" & 统计标签数量("白色") & " 个" & vbCrLf & _
           "红色标签：" & 统计标签数量("红色") & " 个" & vbCrLf & _
           "绿色标签：" & 统计标签数量("绿色") & " 个" & vbCrLf & _
           "用时：" & Format(processTime, "0.00") & " 秒", vbInformation

    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    Exit Sub

ErrorHandler:
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    MsgBox "标签生成出错：" & Err.Description, vbCritical
End Sub
```

### A.2 数据处理模块代码

```vba
'==================================================
' 数据验证和清洗主程序
'==================================================
Sub 数据验证和清洗()
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long
    Dim sampleID As String, scanTime As Variant
    Dim errorCount As Long

    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row

    If lastRow < 2 Then
        MsgBox "没有找到样品数据，请先导入LIMS扫码数据！", vbWarning
        Exit Sub
    End If

    errorCount = 0

    ' 逐行验证数据
    For i = 2 To lastRow
        ' 清除之前的错误标记
        ws.Range(ws.Cells(i, 1), ws.Cells(i, 10)).Interior.Color = xlNone

        ' 验证样品ID
        sampleID = Trim(ws.Cells(i, 1).Value)
        If sampleID = "" Then
            ws.Cells(i, 1).Interior.Color = RGB(255, 200, 200)
            ws.Cells(i, 1).AddComment "样品ID不能为空"
            errorCount = errorCount + 1
        End If

        ' 验证扫码时间
        scanTime = ws.Cells(i, 2).Value
        If Not IsDate(scanTime) Then
            ws.Cells(i, 2).Interior.Color = RGB(255, 200, 200)
            ws.Cells(i, 2).AddComment "扫码时间格式错误"
            errorCount = errorCount + 1
        End If

        ' 数据清洗
        ws.Cells(i, 1).Value = UCase(Trim(sampleID))
        ws.Cells(i, 2).Value = CDate(scanTime)
    Next i

    ' 检查重复样品ID
    Call 检查重复样品ID

    If errorCount > 0 Then
        MsgBox "发现 " & errorCount & " 个数据错误，请检查标红的单元格！", vbWarning
    End If
End Sub
```

### A.3 质控管理模块代码

```vba
'==================================================
' 自动添加质控样品主程序
'==================================================
Sub 自动添加质控样品()
    Dim ws As Worksheet
    Dim qcConfig As String
    Dim sampleCount As Long

    Set ws = ActiveSheet
    sampleCount = 获取样品总数()

    If sampleCount = 0 Then
        MsgBox "没有样品数据，无法添加质控！", vbWarning
        Exit Sub
    End If

    ' 读取质控配置
    qcConfig = Trim(ws.Range("K1").Value)
    If qcConfig = "" Then qcConfig = "10%平行"

    ' 根据配置添加质控
    Select Case qcConfig
        Case "10%平行"
            Call 添加平行样品(Application.WorksheetFunction.RoundUp(sampleCount * 0.1, 0))
        Case "10%加标"
            Call 添加加标样品(Application.WorksheetFunction.RoundUp(sampleCount * 0.1, 0))
        Case "10%平行+10%加标"
            Call 添加平行样品(Application.WorksheetFunction.RoundUp(sampleCount * 0.1, 0))
            Call 添加加标样品(Application.WorksheetFunction.RoundUp(sampleCount * 0.1, 0))
        Case "100%平行"
            Call 添加平行样品(sampleCount)
    End Select

    ' 重新排序
    Call 按时间排序样品
End Sub

'==================================================
' 添加平行样品
'==================================================
Sub 添加平行样品(count As Long)
    Dim ws As Worksheet
    Dim lastRow As Long, insertRow As Long
    Dim interval As Long, i As Long
    Dim sampleID As String

    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row

    If count <= 0 Then Exit Sub

    ' 计算插入间隔
    interval = Int((lastRow - 1) / count)
    If interval < 1 Then interval = 1

    ' 从后往前插入，避免行号变化影响
    For i = count To 1 Step -1
        insertRow = 2 + (i * interval)
        If insertRow > lastRow Then insertRow = lastRow

        sampleID = ws.Cells(insertRow, 1).Value

        ' 插入新行
        ws.Rows(insertRow + 1).Insert Shift:=xlDown

        ' 复制原样品信息
        ws.Range(ws.Cells(insertRow, 1), ws.Cells(insertRow, 12)).Copy
        ws.Range(ws.Cells(insertRow + 1, 1), ws.Cells(insertRow + 1, 12)).PasteSpecial xlPasteValues

        ' 修改为平行样品
        ws.Cells(insertRow + 1, 1).Value = sampleID & "-P" & Format(i, "00")
        ws.Cells(insertRow + 1, 7).Value = "平行样"
        ws.Cells(insertRow + 1, 8).Interior.Color = RGB(255, 255, 0)
        ws.Cells(insertRow + 1, 8).Value = "QC-平行"

        lastRow = lastRow + 1
    Next i

    Application.CutCopyMode = False
End Sub
```

### A.4 标签生成模块代码

```vba
'==================================================
' 生成智能标签主程序
'==================================================
Sub 生成智能标签()
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long
    Dim cycleType As String, labelColor As Long
    Dim sampleCode As String, currentDate As String

    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    currentDate = Format(Date, "yyyymmdd")

    For i = 2 To lastRow
        ' 生成样品编码
        sampleCode = currentDate & Format(i - 1, "000")
        ws.Cells(i, 10).Value = sampleCode

        ' 读取周期类型
        cycleType = Trim(ws.Cells(i, 5).Value)
        If cycleType = "" Then cycleType = "普通"

        ' 设置标签颜色和类型
        Select Case UCase(cycleType)
            Case "普通", "NORMAL", ""
                labelColor = RGB(255, 255, 255)
                ws.Cells(i, 11).Value = "白色标签"
            Case "加急", "URGENT"
                labelColor = RGB(255, 200, 200)
                ws.Cells(i, 11).Value = "红色标签"
            Case "大客户", "VIP"
                labelColor = RGB(200, 255, 200)
                ws.Cells(i, 11).Value = "绿色标签"
            Case Else
                labelColor = RGB(255, 255, 255)
                ws.Cells(i, 11).Value = "白色标签"
        End Select

        ' 应用标签背景色
        ws.Range(ws.Cells(i, 10), ws.Cells(i, 11)).Interior.Color = labelColor

        ' 生成标签内容
        Call 生成单个标签内容(i, sampleCode, cycleType)
    Next i
End Sub

'==================================================
' 生成单个标签内容
'==================================================
Sub 生成单个标签内容(row As Long, sampleCode As String, cycleType As String)
    Dim ws As Worksheet
    Dim labelContent As String
    Dim sampleID As String, projectType As String

    Set ws = ActiveSheet

    sampleID = ws.Cells(row, 1).Value
    projectType = ws.Cells(row, 3).Value

    ' 构建标签内容
    labelContent = "━━━━━━━━━━━━━━━━━━━━" & vbCrLf

    ' 根据周期类型添加标识
    Select Case UCase(cycleType)
        Case "加急", "URGENT"
            labelContent = labelContent & "🔴 加急样品 - 优先处理" & vbCrLf
        Case "大客户", "VIP"
            labelContent = labelContent & "🟢 大客户样品 - 特别关注" & vbCrLf
        Case Else
            labelContent = labelContent & "⚪ 普通样品" & vbCrLf
    End Select

    labelContent = labelContent & "━━━━━━━━━━━━━━━━━━━━" & vbCrLf & _
                   "样品编号：" & sampleCode & vbCrLf & _
                   "原始ID：" & sampleID & vbCrLf & _
                   "检测项目：" & projectType & vbCrLf & _
                   "处理日期：" & Format(Date, "yyyy-mm-dd") & vbCrLf & _
                   "操作员：___________" & vbCrLf & _
                   "━━━━━━━━━━━━━━━━━━━━"

    ws.Cells(row, 12).Value = labelContent
End Sub
```

### A.5 工具函数模块代码

```vba
'==================================================
' 获取样品总数（不包括质控样品）
'==================================================
Function 获取样品总数() As Long
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long, count As Long

    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    count = 0

    For i = 2 To lastRow
        If InStr(ws.Cells(i, 1).Value, "-P") = 0 And InStr(ws.Cells(i, 1).Value, "-S") = 0 Then
            count = count + 1
        End If
    Next i

    获取样品总数 = count
End Function

'==================================================
' 统计标签数量
'==================================================
Function 统计标签数量(labelType As String) As Long
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long, count As Long

    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    count = 0

    For i = 2 To lastRow
        If InStr(ws.Cells(i, 11).Value, labelType) > 0 Then
            count = count + 1
        End If
    Next i

    统计标签数量 = count
End Function

'==================================================
' 按时间排序样品
'==================================================
Sub 按时间排序样品()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim sortRange As Range

    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row

    If lastRow < 3 Then Exit Sub

    Set sortRange = ws.Range("A2:L" & lastRow)

    ' 按扫码时间排序（升序）
    sortRange.Sort Key1:=ws.Range("B2"), Order1:=xlAscending, _
                   Header:=xlNo, OrderCustom:=1, MatchCase:=False, _
                   Orientation:=xlTopToBottom, DataOption1:=xlSortNormal

    ' 重新生成序号
    For i = 2 To lastRow
        ws.Cells(i, 13).Value = i - 1
    Next i
End Sub
```

## 附录B 系统配置参数

### B.1 PDA设备技术规格

| 参数项目 | 技术指标 |
|---------|---------|
| 处理器 | ARM Cortex-A7 1.2GHz |
| 内存 | 1GB RAM + 8GB ROM |
| 显示屏 | 4.0英寸 480×800像素 |
| 扫码引擎 | 一维/二维码扫描 |
| 通信接口 | WiFi、蓝牙、USB |
| 电池容量 | 3000mAh |
| 工作温度 | -10°C ~ +50°C |
| 扫码精度 | >99.9% |
| 扫码速度 | 4秒/样品 |

### B.2 软件系统要求

| 软件组件 | 版本要求 |
|---------|---------|
| 操作系统 | Windows 7/10/11 |
| Office套件 | Microsoft Excel 2010以上 |
| 数据库 | SQL Server 2008以上 |
| 网络环境 | 局域网连接 |
| 内存要求 | 4GB RAM以上 |
| 硬盘空间 | 100GB可用空间 |

### B.3 质控配置参数表

| 质控类型 | 平行比例 | 加标比例 | 适用场景 |
|---------|---------|---------|---------|
| 10%平行 | 10% | 0% | 常规检测 |
| 10%加标 | 0% | 10% | 定量分析 |
| 10%平行+10%加标 | 10% | 10% | 高要求检测 |
| 100%平行 | 100% | 0% | 关键样品 |
| 5%平行+5%加标 | 5% | 5% | 经济型配置 |
| 客户定制 | 自定义 | 自定义 | 特殊要求 |
