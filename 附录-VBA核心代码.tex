\appendix

\section{VBA核心代码}

\subsection{主控制模块代码}

\begin{lstlisting}[caption={按钮1：一键删选并添加平行加标质控},label={lst:button1}]
'==================================================
' 按钮1：一键删选并添加平行加标质控
'==================================================
Sub 按钮1_删选添加质控()
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    
    On Error GoTo ErrorHandler
    
    ' 记录开始时间
    Dim startTime As Double
    startTime = Timer
    
    ' 执行数据处理流程
    Call 数据验证和清洗
    Call 按时间排序样品
    Call 自动添加质控样品
    
    ' 显示处理结果
    Dim processTime As Double
    processTime = Timer - startTime
    
    MsgBox "质控添加完成！" & vbCrLf & _
           "处理样品：" & 获取样品总数() & " 个" & vbCrLf & _
           "添加质控：" & 获取质控总数() & " 个" & vbCrLf & _
           "用时：" & Format(processTime, "0.00") & " 秒", vbInformation
    
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    MsgBox "质控添加出错：" & Err.Description, vbCritical
End Sub
\end{lstlisting}

\begin{lstlisting}[caption={按钮2：生成参数和标签},label={lst:button2}]
'==================================================
' 按钮2：生成参数和标签
'==================================================
Sub 按钮2_生成参数标签()
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    
    On Error GoTo ErrorHandler
    
    Dim startTime As Double
    startTime = Timer
    
    ' 生成称量参数和标签
    Call 生成称量参数
    Call 生成智能标签
    Call 准备打印数据
    
    Dim processTime As Double
    processTime = Timer - startTime
    
    MsgBox "标签生成完成！" & vbCrLf & _
           "生成标签：" & 获取标签总数() & " 个" & vbCrLf & _
           "白色标签：" & 统计标签数量("白色") & " 个" & vbCrLf & _
           "红色标签：" & 统计标签数量("红色") & " 个" & vbCrLf & _
           "绿色标签：" & 统计标签数量("绿色") & " 个" & vbCrLf & _
           "用时：" & Format(processTime, "0.00") & " 秒", vbInformation
    
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    MsgBox "标签生成出错：" & Err.Description, vbCritical
End Sub
\end{lstlisting}

\subsection{数据处理模块代码}

\begin{lstlisting}[caption={数据验证和清洗主程序},label={lst:data_validation}]
'==================================================
' 数据验证和清洗主程序
'==================================================
Sub 数据验证和清洗()
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long
    Dim sampleID As String, scanTime As Variant
    Dim errorCount As Long
    
    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    
    If lastRow < 2 Then
        MsgBox "没有找到样品数据，请先导入LIMS扫码数据！", vbWarning
        Exit Sub
    End If
    
    errorCount = 0
    
    ' 逐行验证数据
    For i = 2 To lastRow
        ' 清除之前的错误标记
        ws.Range(ws.Cells(i, 1), ws.Cells(i, 10)).Interior.Color = xlNone
        
        ' 验证样品ID
        sampleID = Trim(ws.Cells(i, 1).Value)
        If sampleID = "" Then
            ws.Cells(i, 1).Interior.Color = RGB(255, 200, 200)
            ws.Cells(i, 1).AddComment "样品ID不能为空"
            errorCount = errorCount + 1
        End If
        
        ' 验证扫码时间
        scanTime = ws.Cells(i, 2).Value
        If Not IsDate(scanTime) Then
            ws.Cells(i, 2).Interior.Color = RGB(255, 200, 200)
            ws.Cells(i, 2).AddComment "扫码时间格式错误"
            errorCount = errorCount + 1
        End If
        
        ' 数据清洗
        ws.Cells(i, 1).Value = UCase(Trim(sampleID))
        ws.Cells(i, 2).Value = CDate(scanTime)
    Next i
    
    ' 检查重复样品ID
    Call 检查重复样品ID
    
    If errorCount > 0 Then
        MsgBox "发现 " & errorCount & " 个数据错误，请检查标红的单元格！", vbWarning
    End If
End Sub
\end{lstlisting}

\subsection{质控管理模块代码}

\begin{lstlisting}[caption={自动添加质控样品主程序},label={lst:qc_management}]
'==================================================
' 自动添加质控样品主程序
'==================================================
Sub 自动添加质控样品()
    Dim ws As Worksheet
    Dim qcConfig As String
    Dim sampleCount As Long
    
    Set ws = ActiveSheet
    sampleCount = 获取样品总数()
    
    If sampleCount = 0 Then
        MsgBox "没有样品数据，无法添加质控！", vbWarning
        Exit Sub
    End If
    
    ' 读取质控配置
    qcConfig = Trim(ws.Range("K1").Value)
    If qcConfig = "" Then qcConfig = "10%平行"
    
    ' 根据配置添加质控
    Select Case qcConfig
        Case "10%平行"
            Call 添加平行样品(Application.WorksheetFunction.RoundUp(sampleCount * 0.1, 0))
        Case "10%加标"
            Call 添加加标样品(Application.WorksheetFunction.RoundUp(sampleCount * 0.1, 0))
        Case "10%平行+10%加标"
            Call 添加平行样品(Application.WorksheetFunction.RoundUp(sampleCount * 0.1, 0))
            Call 添加加标样品(Application.WorksheetFunction.RoundUp(sampleCount * 0.1, 0))
        Case "100%平行"
            Call 添加平行样品(sampleCount)
    End Select
    
    ' 重新排序
    Call 按时间排序样品
End Sub
\end{lstlisting}

\begin{lstlisting}[caption={添加平行样品},label={lst:add_parallel}]
'==================================================
' 添加平行样品
'==================================================
Sub 添加平行样品(count As Long)
    Dim ws As Worksheet
    Dim lastRow As Long, insertRow As Long
    Dim interval As Long, i As Long
    Dim sampleID As String
    
    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    
    If count <= 0 Then Exit Sub
    
    ' 计算插入间隔
    interval = Int((lastRow - 1) / count)
    If interval < 1 Then interval = 1
    
    ' 从后往前插入，避免行号变化影响
    For i = count To 1 Step -1
        insertRow = 2 + (i * interval)
        If insertRow > lastRow Then insertRow = lastRow
        
        sampleID = ws.Cells(insertRow, 1).Value
        
        ' 插入新行
        ws.Rows(insertRow + 1).Insert Shift:=xlDown
        
        ' 复制原样品信息
        ws.Range(ws.Cells(insertRow, 1), ws.Cells(insertRow, 12)).Copy
        ws.Range(ws.Cells(insertRow + 1, 1), ws.Cells(insertRow + 1, 12)).PasteSpecial xlPasteValues
        
        ' 修改为平行样品
        ws.Cells(insertRow + 1, 1).Value = sampleID & "-P" & Format(i, "00")
        ws.Cells(insertRow + 1, 7).Value = "平行样"
        ws.Cells(insertRow + 1, 8).Interior.Color = RGB(255, 255, 0)
        ws.Cells(insertRow + 1, 8).Value = "QC-平行"
        
        lastRow = lastRow + 1
    Next i
    
    Application.CutCopyMode = False
End Sub
\end{lstlisting}

\subsection{标签生成模块代码}

\begin{lstlisting}[caption={生成智能标签主程序},label={lst:label_generation}]
'==================================================
' 生成智能标签主程序
'==================================================
Sub 生成智能标签()
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long
    Dim cycleType As String, labelColor As Long
    Dim sampleCode As String, currentDate As String
    
    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    currentDate = Format(Date, "yyyymmdd")
    
    For i = 2 To lastRow
        ' 生成样品编码
        sampleCode = currentDate & Format(i - 1, "000")
        ws.Cells(i, 10).Value = sampleCode
        
        ' 读取周期类型
        cycleType = Trim(ws.Cells(i, 5).Value)
        If cycleType = "" Then cycleType = "普通"
        
        ' 设置标签颜色和类型
        Select Case UCase(cycleType)
            Case "普通", "NORMAL", ""
                labelColor = RGB(255, 255, 255)
                ws.Cells(i, 11).Value = "白色标签"
            Case "加急", "URGENT"
                labelColor = RGB(255, 200, 200)
                ws.Cells(i, 11).Value = "红色标签"
            Case "大客户", "VIP"
                labelColor = RGB(200, 255, 200)
                ws.Cells(i, 11).Value = "绿色标签"
            Case Else
                labelColor = RGB(255, 255, 255)
                ws.Cells(i, 11).Value = "白色标签"
        End Select
        
        ' 应用标签背景色
        ws.Range(ws.Cells(i, 10), ws.Cells(i, 11)).Interior.Color = labelColor
        
        ' 生成标签内容
        Call 生成单个标签内容(i, sampleCode, cycleType)
    Next i
End Sub
\end{lstlisting}

\subsection{工具函数模块代码}

\begin{lstlisting}[caption={获取样品总数函数},label={lst:get_sample_count}]
'==================================================
' 获取样品总数（不包括质控样品）
'==================================================
Function 获取样品总数() As Long
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long, count As Long
    
    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    count = 0
    
    For i = 2 To lastRow
        If InStr(ws.Cells(i, 1).Value, "-P") = 0 And InStr(ws.Cells(i, 1).Value, "-S") = 0 Then
            count = count + 1
        End If
    Next i
    
    获取样品总数 = count
End Function
\end{lstlisting}

\begin{lstlisting}[caption={按时间排序样品},label={lst:sort_samples}]
'==================================================
' 按时间排序样品
'==================================================
Sub 按时间排序样品()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim sortRange As Range
    
    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    
    If lastRow < 3 Then Exit Sub
    
    Set sortRange = ws.Range("A2:L" & lastRow)
    
    ' 按扫码时间排序（升序）
    sortRange.Sort Key1:=ws.Range("B2"), Order1:=xlAscending, _
                   Header:=xlNo, OrderCustom:=1, MatchCase:=False, _
                   Orientation:=xlTopToBottom, DataOption1:=xlSortNormal
    
    ' 重新生成序号
    For i = 2 To lastRow
        ws.Cells(i, 13).Value = i - 1
    Next i
End Sub
\end{lstlisting}

\section{系统配置参数}

\subsection{PDA设备技术规格}

\begin{table}[H]
\centering
\caption{PDA设备技术规格}
\label{tab:pda_specs}
\begin{tabular}{cc}
\toprule
参数项目 & 技术指标 \\
\midrule
处理器 & ARM Cortex-A7 1.2GHz \\
内存 & 1GB RAM + 8GB ROM \\
显示屏 & 4.0英寸 480×800像素 \\
扫码引擎 & 一维/二维码扫描 \\
通信接口 & WiFi、蓝牙、USB \\
电池容量 & 3000mAh \\
工作温度 & -10°C \textasciitilde{} +50°C \\
扫码精度 & >99.9\% \\
扫码速度 & 4秒/样品 \\
\bottomrule
\end{tabular}
\end{table}

\subsection{软件系统要求}

\begin{table}[H]
\centering
\caption{软件系统要求}
\label{tab:software_requirements}
\begin{tabular}{cc}
\toprule
软件组件 & 版本要求 \\
\midrule
操作系统 & Windows 7/10/11 \\
Office套件 & Microsoft Excel 2010以上 \\
数据库 & SQL Server 2008以上 \\
网络环境 & 局域网连接 \\
内存要求 & 4GB RAM以上 \\
硬盘空间 & 100GB可用空间 \\
\bottomrule
\end{tabular}
\end{table}

\subsection{质控配置参数表}

\begin{table}[H]
\centering
\caption{质控配置参数表}
\label{tab:qc_config}
\begin{tabular}{cccc}
\toprule
质控类型 & 平行比例 & 加标比例 & 适用场景 \\
\midrule
10\%平行 & 10\% & 0\% & 常规检测 \\
10\%加标 & 0\% & 10\% & 定量分析 \\
10\%平行+10\%加标 & 10\% & 10\% & 高要求检测 \\
100\%平行 & 100\% & 0\% & 关键样品 \\
5\%平行+5\%加标 & 5\% & 5\% & 经济型配置 \\
客户定制 & 自定义 & 自定义 & 特殊要求 \\
\bottomrule
\end{tabular}
\end{table}
