幻灯片1


理化项目
集中称量成果分享
汇报人：郭晓雷
2025.07.28
1
幻灯片2
理化项目集中称量

改  善  前
现状描述：
1、理化项目多且器皿种类杂；
2、测试工程师只负责称自己项目的样品，样品每日被TOUCH次数多，导致每人每天找样时间需要花至少30min以上；
3、手写简要数字序号，测试过程中无法直观知道数字对应是什么样品，出现不同现象样品也是完成后才知道什么样品；
4、测试过程中需要人工挑选质控，方法要求的质控需要人工记忆，对于客户要求的质控容易遗漏。
5、加急、大客户称完样品以后就无法直观的知道哪个是加急哪个是大客户的，导致称样后没有优先概念。
效果：人工工时降低42.12S，每年可节约约11.76万个PE袋。
2
幻灯片3
理化项目集中称量

改  善  前
称量步骤：
1、每个测试工程师对自己检测项目各自系统建批（10min/每人每天）
2、复制已经建批的检测项目到excel中并打印纸张出来看（3min/每人每天）
3、找样品（35min/每人每天）。
4、人工排序样品（按表单顺序）（30个样品为例子，至少10min/每人每天）
5、取器皿、剪塑料一次性吸管变成称量勺（10min/每人每天）
6、玻璃器皿上写数字编号（30个样品为例子，至少10min/每人每天）
7、开始称样

总结：


现状总结：
1、器皿过多，测试工程师每人每天都往洗涤室取器皿，没有集中取增加了不必要的走动。
2、找样品时间长，且每人都打开一次小样袋，效率低，重复的称量动作只能是测试工程师自己完成，无法释放他们做更多的检测工作。
3、每一次的称样都用一支新的取样勺，浪费耗材，浪费人力。
4、加急周期、大客户样品到了检测环节，无法直观的被看到，导致大客户、加急周期很难保证周期。

效果：人工工时降低42.12S，每年可节约约11.76万个PE袋。
3
幻灯片4
理化项目集中称量

改  善  后
称量步骤：
1、PDA扫描待称样的样品二维码，系统自动出现扫码时间（30个样品为例，至多2min完成）。
2、从系统SQL315复制当天扫描样品粘贴到称量表里。（每天4次来样品，复制4次，1min/每次）
3、点击3个宏运行程序后自动生成按扫码时间先后顺序、不同方法或客户要求需求的质控样、称量条件（称样量、容器）、打印生成3种标签（普通、加急、大客户）标签。（1分钟完成程序运行任务）
4、贴标签。（30个样品为例，至多7min完成）
5、开始称样。


效果总结：
1.理化组从原来的发3份样品缩减成发1份。（相同制样方式的原来是无机2、3、水分各发1份，集中称量后仅需1份）。
2.理化组从原来的17人各自称量到现在是整个理化2个员工称量，1名临时工、1名实习生），减少天平的来回等待使用时间浪费。
3.称量器皿统一一次性推车取、平均开小样袋次数从每7次缩减到1次、称样勺耗材从每个样品需要7支缩减到1支，减少耗材用量和人力。
4.重复的称量动作任务分配给临时工、实习生，减少测试工程师的工作量，使在有效的工作时间完成更多的检测任务。
5.3种标签（普通、加急、大客户采用3种不同颜色），有效的将优先概念运转到测试环节。

效果：人工工时降低42.12S，每年可节约约11.76万个PE袋。
4
幻灯片5
理化项目集中称量

改  善  成 果 -称量表的改善    ( 变化比较大的3次比较）
VBA修改了至少20版，不断的增加使用功能，操作按钮也从9个按钮减少至3个，减少操作次数和操作难度。
效果总结：3步完成打印标签，方便操作，简单易行。体现了精益求精，持续改善的精神！
效果：人工工时降低42.12S，每年可节约约11.76万个PE袋。
5
幻灯片6
理化项目集中称量

改  善  成 果-称样表的改善前期
1、改善前不能加质控，平行加标规则需要称量员记忆，方法种类过多，难度巨大。
2、生成的标签也仅生成简要信息，如右图，理化测试过程多种项目并非一种器皿一步解决测试，中间转移器皿次数多且有高温测试的不便贴标签，总需要人工再写样品短号在转移的器皿上，花费很多时间。
3、只有一种白色的普通标签，加急、大客户的称样后的步骤标签无法直观看到；
4、excel表全呈现白色，短号、样品名称类似的非常容易看花、称错。
5、相同短号需要测试的项目用到的器皿不同，部分器皿无法直接站立需要借助工具稳固（如离心管），称量员称一个样品不同项目需要不停的取出再放入辅助称量工具。
6、每天第二次到第四次需要人工选择上一次的打印到哪一个样品，选择范围打印，比较麻烦。

效果：人工工时降低42.12S，每年可节约约11.76万个PE袋。
效果总结：有集中称量的方式了，但是并不是很方便。
6
幻灯片7
理化项目集中称量

改  善  成 果-称样表的改善中期
改善中期：1、增加了自动添加质控功能，程序运行自动添加，标签会呈现如右图，不用再人工记忆。
                   2、增加了器皿排序，相同短号需要测试的项目用到的器皿不同需要用辅助工具垫稳称量的设置在连续的上下行，减少了称样员取出再放入辅助称量工具。
       发现新问题：3.许多项目并非隔天就能全部测完，有堆积，导致过了时间难以找到样品位置。

效果总结：改善后减轻了称量员的工作，实验员的环节工作还未解决。
效果：人工工时降低42.12S，每年可节约约11.76万个PE袋。
7
幻灯片8
理化项目集中称量

改  善  成 果-称样表的改善后期效果图
效果：人工工时降低42.12S，每年可节约约11.76万个PE袋。
8
幻灯片9
理化项目集中称量

改  善  成 果-称样表的改善后期总结
1、在标签的左下角做了一个自动生成数字编码的程序，针对不同的检测项目需求，设置为称量当天日期+序号，序号代表当天此项目称量至多少数量，方便在转移器皿的时候可以写简短的数字编号转递检测环节，减轻检测人员转移器皿抄短号的痛点。

2、根据系统导出的周期类型，生成白色、红色、绿色匹配普通、加急、大客户样品，确保优先级得到保障。

3、样品短号相同的设置相同颜色，避免了称量员因类似短号或样品名称称错样品。
 
4、通过程序记忆打印过的样品标签不在出现，不用再称量员记忆上一次打印到哪里，生成的即是最新需要称量的样品标签。

5、将可以合并的宏程序合并，从9个操作按钮减少至3个按钮，降低操作难度，提升操作速度。

6、在标签上自动根据检测项目放置地点自动生成，在标签上有了具体定位，检测员在固定位置即能找到自己样品，不再需要找样品。
效果总结：彻底解决了称样端和检测端的痛点。
效果：人工工时降低42.12S，每年可节约约11.76万个PE袋。
9
幻灯片10
理化项目集中称量

改  善  成 果-打单表
1、一键删选并添加平行加标：满足质控要求，可以按照标准要求或客户要求生成100%平行或10%加标或10%平行或10%加标平行
2、生成参数：生成称量要求，满足标准要求的基质对应的称样量、称量容器。
3、生成标签：根据周期要求，生成白色、红色、绿色标签
4、一键清空：方便需要清空的时候清空。“一键清空”生成空白表。
效果总结：通过这个表，3步打印标签。
效果：人工工时降低42.12S，每年可节约约11.76万个PE袋。
10
幻灯片11
理化项目集中称量

改  善  成 果-打标签、贴标签成果图
样品标签内容有：出单日期、项目、测试项目的容器、样品位置、样品称量日期+序号！
效果总结：基本运行成熟！
效果：人工工时降低42.12S，每年可节约约11.76万个PE袋。
11
幻灯片12
理化项目集中称量

改  善  成 果-集中称量的改善点
1、称样量直接连接天平采集数据；
2、建在线文档共享表，样品量多时多人共同协助；
3、水分和灰分器皿烘干放置共享表供称量员直接复制粘贴；
-灰分、水分用二维码固定编号
4、相同一个样品同一种颜色表示。


效果总结：提高工作效率至少30%，节约人工至少2个。
效果：人工工时降低42.12S，每年可节约约11.76万个PE袋。
12
幻灯片13
理化项目集中称量

改  善  成 果-集中称量效果图
效果总结：提高工作效率至少30%，节约人工至少2个。
效果：人工工时降低42.12S，每年可节约约11.76万个PE袋。
13
幻灯片14
理化项目集中称量

改  善  过 程
行动执行：
1、生成标签的优化
2、应用VBA，多轮的优化
3、标签颜色、标签内容的优化
4、EXCEL 同一个样品对应颜色的优化
5、KATA工具的学习-PDA扫码应用

总结：多轮改善，现地现物，精益求精！
效果：人工工时降低42.12S，每年可节约约11.76万个PE袋。
14
幻灯片15
改善收益
直接收益分析
幻灯片16
推广与创新
1、操作简单，可易推至其他实验室
2、使用VBA工具，让工作简单易做-3步打印标签
3、根据周期不同，生成不同颜色标签
4、水分、灰分器皿用二维码固定编号，耐洗耐高温


