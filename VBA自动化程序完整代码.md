# VBA自动化程序完整代码

## 程序架构说明

基于您在"称量表宏的聚集.docx"中的宏代码，我将VBA程序分为以下几个核心模块：

1. **主控制模块** - 统一调度各个功能模块
2. **数据处理模块** - 处理PDA扫码数据和验证
3. **排序算法模块** - 按时间戳排序样品
4. **质控管理模块** - 自动添加各类质控样品
5. **标签生成模块** - 智能生成分类标签
6. **协同工作模块** - 支持多人同时操作
7. **工具函数模块** - 通用工具和辅助函数

## 1. 主控制模块

### 1.1 主程序入口 - 三个核心宏按钮

```vba
'==================================================
' 按钮1：一键删选并添加平行加标
'==================================================
Sub 按钮1_删选添加质控()
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual

    On Error GoTo ErrorHandler

    ' 记录开始时间
    Dim startTime As Double
    startTime = Timer

    ' 执行数据处理流程
    Call 数据验证和清洗
    Call 按时间排序样品
    Call 自动添加质控样品

    ' 显示处理结果
    Dim processTime As Double
    processTime = Timer - startTime

    MsgBox "质控添加完成！" & vbCrLf & _
           "处理样品：" & 获取样品总数() & " 个" & vbCrLf & _
           "添加质控：" & 获取质控总数() & " 个" & vbCrLf & _
           "用时：" & Format(processTime, "0.00") & " 秒", vbInformation, "质控处理完成"

    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    Exit Sub

ErrorHandler:
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    MsgBox "质控添加出错：" & Err.Description, vbCritical, "错误"
End Sub

'==================================================
' 按钮2：生成参数和标签
'==================================================
Sub 按钮2_生成参数标签()
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual

    On Error GoTo ErrorHandler

    Dim startTime As Double
    startTime = Timer

    ' 生成称量参数
    Call 生成称量参数

    ' 生成智能标签
    Call 生成智能标签

    ' 准备打印数据
    Call 准备打印数据

    Dim processTime As Double
    processTime = Timer - startTime

    MsgBox "标签生成完成！" & vbCrLf & _
           "生成标签：" & 获取标签总数() & " 个" & vbCrLf & _
           "白色标签：" & 统计标签数量("白色") & " 个" & vbCrLf & _
           "红色标签：" & 统计标签数量("红色") & " 个" & vbCrLf & _
           "绿色标签：" & 统计标签数量("绿色") & " 个" & vbCrLf & _
           "用时：" & Format(processTime, "0.00") & " 秒", vbInformation, "标签生成完成"

    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    Exit Sub

ErrorHandler:
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    MsgBox "标签生成出错：" & Err.Description, vbCritical, "错误"
End Sub

'==================================================
' 按钮3：一键清空
'==================================================
Sub 按钮3_一键清空()
    Dim response As VbMsgBoxResult
    response = MsgBox("确定要清空所有数据吗？此操作不可撤销！", vbYesNo + vbWarning, "确认清空")

    If response = vbYes Then
        Application.ScreenUpdating = False

        ' 清空数据区域
        Call 清空数据区域

        ' 重置计数器
        Call 重置计数器

        ' 清空打印队列
        Call 清空打印队列

        Application.ScreenUpdating = True
        MsgBox "数据清空完成！", vbInformation, "清空完成"
    End If
End Sub
```

## 2. 数据处理模块

### 2.1 数据验证和清洗

```vba
'==================================================
' 数据验证和清洗主程序
'==================================================
Sub 数据验证和清洗()
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long
    Dim sampleID As String, scanTime As Variant
    Dim projectType As String, cycleType As String
    Dim errorCount As Long

    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row

    If lastRow < 2 Then
        MsgBox "没有找到样品数据，请先导入PDA扫码数据！", vbWarning, "数据为空"
        Exit Sub
    End If

    errorCount = 0

    ' 逐行验证数据
    For i = 2 To lastRow
        ' 清除之前的错误标记
        ws.Range(ws.Cells(i, 1), ws.Cells(i, 10)).Interior.Color = xlNone

        ' 验证样品ID
        sampleID = Trim(ws.Cells(i, 1).Value)
        If sampleID = "" Then
            ws.Cells(i, 1).Interior.Color = RGB(255, 200, 200)
            ws.Cells(i, 1).AddComment "样品ID不能为空"
            errorCount = errorCount + 1
        ElseIf Len(sampleID) < 3 Then
            ws.Cells(i, 1).Interior.Color = RGB(255, 200, 200)
            ws.Cells(i, 1).AddComment "样品ID长度不能少于3位"
            errorCount = errorCount + 1
        End If

        ' 验证扫码时间
        scanTime = ws.Cells(i, 2).Value
        If Not IsDate(scanTime) Then
            ws.Cells(i, 2).Interior.Color = RGB(255, 200, 200)
            ws.Cells(i, 2).AddComment "扫码时间格式错误"
            errorCount = errorCount + 1
        End If

        ' 验证项目类型
        projectType = Trim(ws.Cells(i, 3).Value)
        If projectType = "" Then
            ws.Cells(i, 3).Value = "理化检测" ' 默认值
            ws.Cells(i, 3).Interior.Color = RGB(255, 255, 200)
            ws.Cells(i, 3).AddComment "已设置默认项目类型"
        End If

        ' 验证周期类型
        cycleType = Trim(ws.Cells(i, 5).Value)
        If cycleType = "" Then
            ws.Cells(i, 5).Value = "普通" ' 默认值
            ws.Cells(i, 5).Interior.Color = RGB(255, 255, 200)
            ws.Cells(i, 5).AddComment "已设置默认周期类型"
        End If

        ' 数据清洗
        ws.Cells(i, 1).Value = UCase(Trim(sampleID)) ' 样品ID转大写
        ws.Cells(i, 2).Value = CDate(scanTime) ' 标准化时间格式
        ws.Cells(i, 3).Value = Trim(projectType)
        ws.Cells(i, 5).Value = Trim(cycleType)
    Next i

    ' 检查重复样品ID
    Call 检查重复样品ID

    If errorCount > 0 Then
        MsgBox "发现 " & errorCount & " 个数据错误，请检查标红的单元格！", vbWarning, "数据验证"
    End If
End Sub

'==================================================
' 检查重复样品ID
'==================================================
Sub 检查重复样品ID()
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long, j As Long
    Dim sampleID As String
    Dim duplicateCount As Long

    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    duplicateCount = 0

    For i = 2 To lastRow - 1
        sampleID = ws.Cells(i, 1).Value
        For j = i + 1 To lastRow
            If ws.Cells(j, 1).Value = sampleID Then
                ws.Cells(i, 1).Interior.Color = RGB(255, 150, 150)
                ws.Cells(j, 1).Interior.Color = RGB(255, 150, 150)
                ws.Cells(i, 1).AddComment "重复的样品ID: " & sampleID
                ws.Cells(j, 1).AddComment "重复的样品ID: " & sampleID
                duplicateCount = duplicateCount + 1
            End If
        Next j
    Next i

    If duplicateCount > 0 Then
        MsgBox "发现 " & duplicateCount & " 个重复的样品ID，请检查！", vbWarning, "重复检查"
    End If
End Sub
```

## 3. 排序算法模块

### 3.1 按时间排序样品

```vba
'==================================================
' 按扫码时间排序样品
'==================================================
Sub 按时间排序样品()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim sortRange As Range
    Dim i As Long

    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row

    If lastRow < 3 Then
        MsgBox "样品数量不足，无需排序！", vbInformation, "排序提示"
        Exit Sub
    End If

    ' 定义排序范围（包含所有数据列）
    Set sortRange = ws.Range("A2:L" & lastRow)

    ' 执行排序（按扫码时间升序）
    sortRange.Sort Key1:=ws.Range("B2"), Order1:=xlAscending, _
                   Header:=xlNo, OrderCustom:=1, MatchCase:=False, _
                   Orientation:=xlTopToBottom, DataOption1:=xlSortNormal

    ' 重新生成序号
    For i = 2 To lastRow
        ws.Cells(i, 13).Value = i - 1 ' 序号列
    Next i

    ' 相同样品编号设置相同颜色
    Call 设置相同样品颜色

End Sub

'==================================================
' 为相同样品编号设置相同颜色
'==================================================
Sub 设置相同样品颜色()
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long
    Dim sampleID As String, prevSampleID As String
    Dim colorIndex As Long
    Dim colors As Variant

    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row

    ' 定义颜色数组
    colors = Array(RGB(230, 230, 250), RGB(250, 230, 230), RGB(230, 250, 230), _
                   RGB(250, 250, 230), RGB(230, 250, 250), RGB(250, 230, 250))

    colorIndex = 0
    prevSampleID = ""

    For i = 2 To lastRow
        sampleID = Left(ws.Cells(i, 1).Value, InStr(ws.Cells(i, 1).Value & "-", "-") - 1)

        If sampleID <> prevSampleID Then
            colorIndex = (colorIndex + 1) Mod UBound(colors) + 1
            prevSampleID = sampleID
        End If

        ' 应用颜色（仅对非质控样品）
        If InStr(ws.Cells(i, 1).Value, "-P") = 0 And InStr(ws.Cells(i, 1).Value, "-S") = 0 Then
            ws.Range(ws.Cells(i, 1), ws.Cells(i, 6)).Interior.Color = colors(colorIndex - 1)
        End If
    Next i
End Sub
```

## 4. 质控管理模块

### 4.1 自动添加质控样品主程序

```vba
'==================================================
' 自动添加质控样品主程序
'==================================================
Sub 自动添加质控样品()
    Dim ws As Worksheet
    Dim qcConfig As String
    Dim sampleCount As Long

    Set ws = ActiveSheet
    sampleCount = 获取样品总数()

    If sampleCount = 0 Then
        MsgBox "没有样品数据，无法添加质控！", vbWarning, "质控添加"
        Exit Sub
    End If

    ' 读取质控配置（从配置单元格K1）
    qcConfig = Trim(ws.Range("K1").Value)
    If qcConfig = "" Then qcConfig = "10%平行" ' 默认配置

    ' 根据配置添加质控
    Select Case qcConfig
        Case "10%平行"
            Call 添加平行样品(Application.WorksheetFunction.RoundUp(sampleCount * 0.1, 0))
        Case "10%加标"
            Call 添加加标样品(Application.WorksheetFunction.RoundUp(sampleCount * 0.1, 0))
        Case "10%平行+10%加标"
            Call 添加平行样品(Application.WorksheetFunction.RoundUp(sampleCount * 0.1, 0))
            Call 添加加标样品(Application.WorksheetFunction.RoundUp(sampleCount * 0.1, 0))
        Case "100%平行"
            Call 添加平行样品(sampleCount)
        Case "5%平行+5%加标"
            Call 添加平行样品(Application.WorksheetFunction.RoundUp(sampleCount * 0.05, 0))
            Call 添加加标样品(Application.WorksheetFunction.RoundUp(sampleCount * 0.05, 0))
        Case "无质控"
            ' 不添加质控
        Case Else
            MsgBox "未识别的质控配置：" & qcConfig, vbWarning, "质控配置错误"
    End Select

    ' 重新排序（质控样品插入后）
    Call 按时间排序样品
End Sub
```

### 4.2 添加平行样品

```vba
'==================================================
' 添加平行样品
'==================================================
Sub 添加平行样品(count As Long)
    Dim ws As Worksheet
    Dim lastRow As Long, insertRow As Long
    Dim interval As Long, i As Long, actualCount As Long
    Dim sampleID As String

    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row

    If count <= 0 Then Exit Sub

    ' 计算插入间隔
    interval = Int((lastRow - 1) / count)
    If interval < 1 Then interval = 1

    actualCount = 0

    ' 从后往前插入，避免行号变化影响
    For i = count To 1 Step -1
        insertRow = 2 + (i * interval)
        If insertRow > lastRow Then insertRow = lastRow

        ' 确保不在质控样品后插入
        Do While InStr(ws.Cells(insertRow, 1).Value, "-P") > 0 Or InStr(ws.Cells(insertRow, 1).Value, "-S") > 0
            insertRow = insertRow - 1
            If insertRow < 2 Then insertRow = 2: Exit Do
        Loop

        sampleID = ws.Cells(insertRow, 1).Value

        ' 插入新行
        ws.Rows(insertRow + 1).Insert Shift:=xlDown

        ' 复制原样品信息
        ws.Range(ws.Cells(insertRow, 1), ws.Cells(insertRow, 12)).Copy
        ws.Range(ws.Cells(insertRow + 1, 1), ws.Cells(insertRow + 1, 12)).PasteSpecial xlPasteValues

        ' 修改为平行样品
        ws.Cells(insertRow + 1, 1).Value = sampleID & "-P" & Format(actualCount + 1, "00")
        ws.Cells(insertRow + 1, 7).Value = "平行样"
        ws.Cells(insertRow + 1, 8).Interior.Color = RGB(255, 255, 0) ' 黄色标识
        ws.Cells(insertRow + 1, 8).Value = "QC-平行"

        actualCount = actualCount + 1
        lastRow = lastRow + 1
    Next i

    Application.CutCopyMode = False

    ' 记录质控添加日志
    Call 记录质控日志("平行样", actualCount)
End Sub

'==================================================
' 添加加标样品
'==================================================
Sub 添加加标样品(count As Long)
    Dim ws As Worksheet
    Dim lastRow As Long, insertRow As Long
    Dim interval As Long, i As Long, actualCount As Long
    Dim sampleID As String

    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row

    If count <= 0 Then Exit Sub

    ' 计算插入间隔
    interval = Int((lastRow - 1) / count)
    If interval < 1 Then interval = 1

    actualCount = 0

    ' 从后往前插入
    For i = count To 1 Step -1
        insertRow = 2 + (i * interval) + Int(i / 2) ' 与平行样错开
        If insertRow > lastRow Then insertRow = lastRow

        ' 确保不在质控样品后插入
        Do While InStr(ws.Cells(insertRow, 1).Value, "-P") > 0 Or InStr(ws.Cells(insertRow, 1).Value, "-S") > 0
            insertRow = insertRow - 1
            If insertRow < 2 Then insertRow = 2: Exit Do
        Loop

        sampleID = ws.Cells(insertRow, 1).Value

        ' 插入新行
        ws.Rows(insertRow + 1).Insert Shift:=xlDown

        ' 复制原样品信息
        ws.Range(ws.Cells(insertRow, 1), ws.Cells(insertRow, 12)).Copy
        ws.Range(ws.Cells(insertRow + 1, 1), ws.Cells(insertRow + 1, 12)).PasteSpecial xlPasteValues

        ' 修改为加标样品
        ws.Cells(insertRow + 1, 1).Value = sampleID & "-S" & Format(actualCount + 1, "00")
        ws.Cells(insertRow + 1, 7).Value = "加标样"
        ws.Cells(insertRow + 1, 8).Interior.Color = RGB(0, 255, 255) ' 青色标识
        ws.Cells(insertRow + 1, 8).Value = "QC-加标"

        actualCount = actualCount + 1
        lastRow = lastRow + 1
    Next i

    Application.CutCopyMode = False

    ' 记录质控添加日志
    Call 记录质控日志("加标样", actualCount)
End Sub
```

## 5. 标签生成模块

### 5.1 生成智能标签主程序

```vba
'==================================================
' 生成智能标签主程序
'==================================================
Sub 生成智能标签()
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long
    Dim cycleType As String, labelColor As Long
    Dim sampleCode As String, currentDate As String
    Dim labelCount As Long

    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    currentDate = Format(Date, "yyyymmdd")
    labelCount = 0

    ' 清除已有标签信息
    ws.Range("J2:L" & lastRow).ClearContents
    ws.Range("J2:L" & lastRow).Interior.Color = xlNone

    For i = 2 To lastRow
        ' 生成样品编码
        sampleCode = currentDate & Format(i - 1, "000")
        ws.Cells(i, 10).Value = sampleCode

        ' 读取周期类型
        cycleType = Trim(ws.Cells(i, 5).Value)
        If cycleType = "" Then cycleType = "普通"

        ' 设置标签颜色和类型
        Select Case UCase(cycleType)
            Case "普通", "NORMAL", ""
                labelColor = RGB(255, 255, 255) ' 白色
                ws.Cells(i, 11).Value = "白色标签"
            Case "加急", "URGENT", "紧急"
                labelColor = RGB(255, 200, 200) ' 浅红色
                ws.Cells(i, 11).Value = "红色标签"
            Case "大客户", "VIP", "重要客户"
                labelColor = RGB(200, 255, 200) ' 浅绿色
                ws.Cells(i, 11).Value = "绿色标签"
            Case Else
                labelColor = RGB(255, 255, 255) ' 默认白色
                ws.Cells(i, 11).Value = "白色标签"
        End Select

        ' 应用标签背景色
        ws.Range(ws.Cells(i, 10), ws.Cells(i, 11)).Interior.Color = labelColor

        ' 生成标签内容
        Call 生成单个标签内容(i, sampleCode, cycleType)

        labelCount = labelCount + 1
    Next i

    ' 生成标签统计
    Call 生成标签统计

    MsgBox "成功生成 " & labelCount & " 个标签！", vbInformation, "标签生成完成"
End Sub

'==================================================
' 生成单个标签内容
'==================================================
Sub 生成单个标签内容(row As Long, sampleCode As String, cycleType As String)
    Dim ws As Worksheet
    Dim labelContent As String
    Dim sampleID As String, projectType As String, containerType As String
    Dim position As String, qcType As String

    Set ws = ActiveSheet

    sampleID = ws.Cells(row, 1).Value
    projectType = ws.Cells(row, 3).Value
    containerType = ws.Cells(row, 4).Value
    position = ws.Cells(row, 6).Value
    qcType = ws.Cells(row, 8).Value

    ' 如果位置为空，自动分配
    If position = "" Then
        position = 自动分配位置(row, cycleType)
        ws.Cells(row, 6).Value = position
    End If

    ' 如果容器类型为空，自动分配
    If containerType = "" Then
        containerType = 自动分配容器(projectType)
        ws.Cells(row, 4).Value = containerType
    End If

    ' 构建标签内容
    labelContent = "━━━━━━━━━━━━━━━━━━━━" & vbCrLf

    ' 根据周期类型添加标识
    Select Case UCase(cycleType)
        Case "加急", "URGENT"
            labelContent = labelContent & "🔴 加急样品 - 优先处理" & vbCrLf
        Case "大客户", "VIP"
            labelContent = labelContent & "🟢 大客户样品 - 特别关注" & vbCrLf
        Case Else
            labelContent = labelContent & "⚪ 普通样品" & vbCrLf
    End Select

    labelContent = labelContent & "━━━━━━━━━━━━━━━━━━━━" & vbCrLf & _
                   "样品编号：" & sampleCode & vbCrLf & _
                   "原始ID：" & sampleID & vbCrLf & _
                   "检测项目：" & projectType & vbCrLf & _
                   "容器要求：" & containerType & vbCrLf & _
                   "存放位置：" & position & vbCrLf & _
                   "处理日期：" & Format(Date, "yyyy-mm-dd") & vbCrLf

    ' 如果是质控样品，添加质控信息
    If qcType <> "" Then
        labelContent = labelContent & "质控类型：" & qcType & vbCrLf
    End If

    labelContent = labelContent & "操作员：___________" & vbCrLf & _
                   "━━━━━━━━━━━━━━━━━━━━"

    ws.Cells(row, 12).Value = labelContent
End Sub
```

## 6. 工具函数模块

### 6.1 通用工具函数

```vba
'==================================================
' 获取样品总数（不包括质控样品）
'==================================================
Function 获取样品总数() As Long
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long, count As Long

    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    count = 0

    For i = 2 To lastRow
        If InStr(ws.Cells(i, 1).Value, "-P") = 0 And InStr(ws.Cells(i, 1).Value, "-S") = 0 Then
            count = count + 1
        End If
    Next i

    获取样品总数 = count
End Function

'==================================================
' 获取质控总数
'==================================================
Function 获取质控总数() As Long
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long, count As Long

    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    count = 0

    For i = 2 To lastRow
        If InStr(ws.Cells(i, 1).Value, "-P") > 0 Or InStr(ws.Cells(i, 1).Value, "-S") > 0 Then
            count = count + 1
        End If
    Next i

    获取质控总数 = count
End Function

'==================================================
' 统计标签数量
'==================================================
Function 统计标签数量(labelType As String) As Long
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long, count As Long

    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    count = 0

    For i = 2 To lastRow
        If InStr(ws.Cells(i, 11).Value, labelType) > 0 Then
            count = count + 1
        End If
    Next i

    统计标签数量 = count
End Function

'==================================================
' 自动分配存放位置
'==================================================
Function 自动分配位置(row As Long, cycleType As String) As String
    Dim position As String
    Dim zonePrefix As String

    ' 根据周期类型确定区域前缀
    Select Case UCase(cycleType)
        Case "加急", "URGENT"
            zonePrefix = "A区-" ' 加急样品放A区
        Case "大客户", "VIP"
            zonePrefix = "B区-" ' 大客户样品放B区
        Case Else
            zonePrefix = "C区-" ' 普通样品放C区
    End Select

    position = zonePrefix & Format(row - 1, "00") & "号位"
    自动分配位置 = position
End Function

'==================================================
' 自动分配容器类型
'==================================================
Function 自动分配容器(projectType As String) As String
    Dim container As String

    Select Case UCase(projectType)
        Case "理化检测", "理化全项"
            container = "250ml烧杯"
        Case "重金属检测"
            container = "100ml烧杯"
        Case "农残检测"
            container = "500ml烧杯"
        Case "微生物检测"
            container = "无菌袋"
        Case "水分检测"
            container = "称量皿"
        Case "灰分检测"
            container = "坩埚"
        Case Else
            container = "250ml烧杯" ' 默认容器
    End Select

    自动分配容器 = container
End Function

'==================================================
' 清空数据区域
'==================================================
Sub 清空数据区域()
    Dim ws As Worksheet
    Dim lastRow As Long

    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row

    If lastRow > 1 Then
        ws.Range("A2:M" & lastRow).ClearContents
        ws.Range("A2:M" & lastRow).Interior.Color = xlNone
        ws.Range("A2:M" & lastRow).ClearComments
    End If
End Sub

'==================================================
' 记录质控日志
'==================================================
Sub 记录质控日志(qcType As String, count As Long)
    Dim ws As Worksheet
    Dim logRow As Long

    ' 在日志工作表中记录（如果不存在则创建）
    On Error Resume Next
    Set ws = ThisWorkbook.Worksheets("质控日志")
    On Error GoTo 0

    If ws Is Nothing Then
        Set ws = ThisWorkbook.Worksheets.Add
        ws.Name = "质控日志"
        ws.Cells(1, 1).Value = "日期时间"
        ws.Cells(1, 2).Value = "质控类型"
        ws.Cells(1, 3).Value = "添加数量"
        ws.Cells(1, 4).Value = "操作员"
    End If

    logRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row + 1
    ws.Cells(logRow, 1).Value = Now
    ws.Cells(logRow, 2).Value = qcType
    ws.Cells(logRow, 3).Value = count
    ws.Cells(logRow, 4).Value = Application.UserName
End Sub
```

## 程序使用说明

### 操作步骤：
1. **数据导入**：将PDA扫码数据粘贴到A列开始的区域
2. **配置质控**：在K1单元格设置质控要求（如"10%平行+10%加标"）
3. **点击按钮1**：执行数据验证、排序和质控添加
4. **点击按钮2**：生成称量参数和智能标签
5. **打印标签**：根据生成的标签内容进行打印
6. **必要时点击按钮3**：清空所有数据重新开始

### 程序特点：
- **容错性强**：完善的错误检测和处理机制
- **自动化程度高**：三步操作完成所有处理
- **可配置性好**：支持多种质控模式配置
- **用户友好**：详细的进度提示和结果反馈
