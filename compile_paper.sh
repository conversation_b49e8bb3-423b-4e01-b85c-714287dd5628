#!/bin/bash

# 编译LaTeX论文为PDF的脚本
# 需要安装texlive-full或相应的LaTeX发行版

echo "开始编译LaTeX论文..."

# 检查是否安装了xelatex
if ! command -v xelatex &> /dev/null; then
    echo "错误：未找到xelatex命令。请安装LaTeX发行版（如texlive）"
    echo "Ubuntu/Debian: sudo apt-get install texlive-full"
    echo "CentOS/RHEL: sudo yum install texlive-scheme-full"
    echo "macOS: brew install --cask mactex"
    exit 1
fi

# 设置文件名
MAIN_FILE="基于LIMS系统的VBA理化集中称量流程自动化方法研究"
APPENDIX_FILE="附录-VBA核心代码"

echo "编译主文档..."
# 编译主文档（需要多次编译以处理交叉引用）
xelatex "${MAIN_FILE}.tex"
xelatex "${MAIN_FILE}.tex"

echo "编译附录文档..."
# 编译附录文档
xelatex "${APPENDIX_FILE}.tex"

# 检查编译结果
if [ -f "${MAIN_FILE}.pdf" ]; then
    echo "✅ 主文档编译成功：${MAIN_FILE}.pdf"
else
    echo "❌ 主文档编译失败"
fi

if [ -f "${APPENDIX_FILE}.pdf" ]; then
    echo "✅ 附录文档编译成功：${APPENDIX_FILE}.pdf"
else
    echo "❌ 附录文档编译失败"
fi

# 清理临时文件
echo "清理临时文件..."
rm -f *.aux *.log *.out *.toc *.lof *.lot *.bbl *.blg *.fdb_latexmk *.fls *.synctex.gz

echo "编译完成！"

# 如果在Linux环境下，尝试打开PDF
if command -v xdg-open &> /dev/null && [ -f "${MAIN_FILE}.pdf" ]; then
    echo "正在打开PDF文件..."
    xdg-open "${MAIN_FILE}.pdf" &
fi
